# Script para calcular previsão específica
library(nnet)

# Carregar dados de todas as ligas (mesmo processo do Script 2)
arquivos_csv <- list.files(pattern = "\\.csv$")
lista_dados_organizados <- list()

for (arquivo in arquivos_csv) {
  dados <- read.csv2(arquivo)
  n_times <- nrow(dados)
  n_partidas <- n_times * (n_times - 1)
  
  dados_organizados <- as.data.frame(matrix(NA, nrow = n_partidas, ncol = 4))
  colnames(dados_organizados) <- c("Time_da_casa", "Gols_feitos_pelo_time_da_casa", 
                                   "Gols_feitos_pelo_time_de_fora_de_casa", "Time_de_fora_de_casa")
  
  for (linha in 1:n_times) {
    linha_inicial <- 1 + (linha - 1) * (n_times - 1)
    linha_final <- linha * (n_times - 1)
    
    dados_organizados$Time_da_casa[linha_inicial:linha_final] <- rep(
      dados$Home...Away[linha], (n_times - 1)
    )
    
    dados_organizados$Time_de_fora_de_casa[linha_inicial:linha_final] <- dados$Home...Away[
      which(dados$Home...Away != dados$Home...Away[linha])
    ]
    
    colunas_com_placar <- grep(" x ", dados[linha, ])
    placares <- unlist(dados[linha, colunas_com_placar])
    
    dados_organizados[linha_inicial:linha_final, 2:3] <- as.numeric(
      do.call(rbind, strsplit(placares, " x "))
    )
  }
  
  # Mesclar ratings
  dados_organizados <- merge(dados_organizados, dados, 
                            by.x = "Time_da_casa", by.y = "Home...Away", all.x = TRUE)
  dados_organizados <- dados_organizados[, c(1:4, n_times + 5)]
  colnames(dados_organizados)[5] <- "Rating_do_time_da_casa"
  
  dados_organizados <- merge(dados_organizados, dados, 
                            by.x = "Time_de_fora_de_casa", by.y = "Home...Away", all.x = TRUE)
  dados_organizados <- dados_organizados[, c(2, 1, 3:5, n_times + 6)]
  colnames(dados_organizados)[6] <- "Rating_do_time_de_fora_de_casa"
  
  # Calcular resultado
  dados_organizados$Resultado_do_time_da_casa <- "Empate"
  dados_organizados$Resultado_do_time_da_casa[
    dados_organizados$Gols_feitos_pelo_time_da_casa > dados_organizados$Gols_feitos_pelo_time_de_fora_de_casa
  ] <- "Vitória"
  dados_organizados$Resultado_do_time_da_casa[
    dados_organizados$Gols_feitos_pelo_time_da_casa < dados_organizados$Gols_feitos_pelo_time_de_fora_de_casa
  ] <- "Derrota"
  
  dados_organizados$Resultado_do_time_da_casa <- factor(
    dados_organizados$Resultado_do_time_da_casa,
    levels = c("Derrota", "Empate", "Vitória")
  )
  
  dados_organizados$Diferenca_de_ratings <- dados_organizados$Rating_do_time_da_casa - dados_organizados$Rating_do_time_de_fora_de_casa
  
  lista_dados_organizados[[arquivo]] <- dados_organizados
}

# Combinar dados e treinar modelo
dados_organizados_combinados <- do.call(rbind, lista_dados_organizados)
modelo <- multinom(Resultado_do_time_da_casa ~ Diferenca_de_ratings, data = dados_organizados_combinados)

# Calcular previsão para LDU vs Botafogo
diferenca_rating <- -3.5  # 80.8 - 84.3
novo_dado <- data.frame(Diferenca_de_ratings = diferenca_rating)
probabilidades <- predict(modelo, newdata = novo_dado, type = "probs")

# Mostrar resultados
cat("=== PREVISÃO: LDU vs BOTAFOGO ===\n")
cat("LDU (casa): rating 80.8\n")
cat("Botafogo (visitante): rating 84.3\n")
cat("Diferença de rating: -3.5 (a favor do Botafogo)\n\n")
cat("PROBABILIDADES:\n")
cat("🔴 Derrota da LDU:", round(probabilidades[1]*100, 1), "%\n")
cat("🟡 Empate:", round(probabilidades[2]*100, 1), "%\n")
cat("🟢 Vitória da LDU:", round(probabilidades[3]*100, 1), "%\n")
