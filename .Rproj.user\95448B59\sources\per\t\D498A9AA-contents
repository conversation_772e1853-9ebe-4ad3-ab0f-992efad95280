library(tidyverse)
library(nnet)

# Listar todos os arquivos CSV no diretório atual
arquivos_csv <- list.files(pattern = "\\.csv$")

# Inicializar lista para armazenar dados organizados de cada liga
lista_dados_organizados <- list()

# Loop por cada arquivo CSV
for (arquivo in arquivos_csv) {
  
  # Ler o arquivo
  dados <- read.csv2(arquivo)
  
  # Número de times (linhas da tabela original)
  n_times <- nrow(dados)
  
  # Número de partidas: cada time joga contra todos os outros em casa
  n_partidas <- n_times * (n_times - 1)
  
  # Criar a estrutura para armazenar os dados organizados
  dados_organizados <- as.data.frame(matrix(NA, nrow = n_partidas, ncol = 4))
  colnames(dados_organizados) <- c("Time_da_casa",
                                   "Gols_feitos_pelo_time_da_casa",
                                   "Gols_feitos_pelo_time_de_fora_de_casa",
                                   "Time_de_fora_de_casa")
  
  # Preencher os dados jogo a jogo
  for (linha in 1:n_times) {
    linha_inicial <- 1 + (linha - 1) * (n_times - 1)
    linha_final <- linha * (n_times - 1)
    
    dados_organizados$Time_da_casa[linha_inicial:linha_final] <- rep(
      dados$Home...Away[linha], (n_times - 1)
    )
    
    dados_organizados$Time_de_fora_de_casa[linha_inicial:linha_final] <- dados$Home...Away[
      which(dados$Home...Away != dados$Home...Away[linha])
    ]
    
    colunas_com_placar <- grep(" x ", dados[linha, ])
    placares <- unlist(dados[linha, colunas_com_placar])
    
    dados_organizados[linha_inicial:linha_final, 2:3] <- as.numeric(
      do.call(rbind, strsplit(placares, " x "))
    )
  }
  
  # Mesclar os ratings dos times da casa
  dados_organizados <- dados_organizados %>%
    merge(dados, by.x = "Time_da_casa", by.y = "Home...Away", all.x = TRUE) %>%
    select(1:4, n_times + 5) %>%
    rename(Rating_do_time_da_casa = 5) %>%
    
    # Mesclar os ratings dos times visitantes
    merge(dados, by.x = "Time_de_fora_de_casa", by.y = "Home...Away", all.x = TRUE) %>%
    select(2, 1, 3:5, n_times + 6) %>%
    rename(Rating_do_time_de_fora_de_casa = 6)
  
  # Calcular o resultado do time da casa
  dados_organizados$Resultado_do_time_da_casa <- "Empate"
  dados_organizados$Resultado_do_time_da_casa[
    dados_organizados$Gols_feitos_pelo_time_da_casa > dados_organizados$Gols_feitos_pelo_time_de_fora_de_casa
  ] <- "Vitória"
  dados_organizados$Resultado_do_time_da_casa[
    dados_organizados$Gols_feitos_pelo_time_da_casa < dados_organizados$Gols_feitos_pelo_time_de_fora_de_casa
  ] <- "Derrota"
  
  # Converter para fator ordenado
  dados_organizados$Resultado_do_time_da_casa <- factor(
    dados_organizados$Resultado_do_time_da_casa,
    levels = c("Derrota", "Empate", "Vitória")
  )
  
  # Calcular a diferença de ratings
  dados_organizados$Diferenca_de_ratings <- dados_organizados$Rating_do_time_da_casa - dados_organizados$Rating_do_time_de_fora_de_casa
  
  # Armazenar na lista
  lista_dados_organizados[[arquivo]] <- dados_organizados
}

# Unir todos os data frames da lista em um único
dados_organizados_combinados <- do.call(rbind, lista_dados_organizados)

# Ajustar o modelo de regressão multinomial
modelo <- multinom(
  Resultado_do_time_da_casa ~ Diferenca_de_ratings,
  data = dados_organizados_combinados
)

# Resumo do modelo
summary(modelo)

# Previsão para novo dado
novo_dado <- data.frame(Diferenca_de_ratings = 2.2)
predict(modelo, newdata = novo_dado, type = "probs")
