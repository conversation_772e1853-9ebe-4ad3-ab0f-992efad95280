# library(tidyverse)  # Comentado para usar apenas funções base do R
library(nnet)
dados <- read.csv2("2024-25 English Premier League results.csv")
dados_organizados <- as.data.frame(matrix(NA, nrow = 380, ncol = 4))
colnames(dados_organizados) <- c("Time_da_casa",
                                 "Gols_feitos_pelo_time_da_casa",
                                 "Gols_feitos_pelo_time_de_fora_de_casa",
                                 "Time_de_fora_de_casa")
for(linha in c(1 : 20)){
  linha_inicial <- 1 + (linha - 1) * 19
  linha_final <- linha * 19
  dados_organizados$Time_da_casa[c(linha_inicial : linha_final)] <- rep(
    x = dados$Home...Away[linha],
    times = 19
  )
  dados_organizados$Time_de_fora_de_casa[c(linha_inicial : linha_final)] <- dados$Home...Away[which(
    dados$Home...Away != dados$Home...Away[linha]
  )]
  colunas_com_placar <- grep(x = dados[linha, ], pattern = " x ")
  placares_do_time_em_casa <- as.vector(dados[linha, ][colunas_com_placar])
  names(placares_do_time_em_casa) <- c()
  placares_do_time_em_casa <- unlist(placares_do_time_em_casa)
  dados_organizados[c(linha_inicial : linha_final), c(2, 3)] <- as.numeric(
    do.call(
      rbind,
      strsplit(
        x = placares_do_time_em_casa,
        split = " x "
      )
    )
  )
}
dados_organizados <- merge(dados_organizados, dados,
                                  by.x = "Time_da_casa",
                                  by.y = "Home...Away",
                                  all.x = TRUE)
dados_organizados <- dados_organizados[, c(1:4, 25)]
colnames(dados_organizados)[5] <- "Rating_do_time_da_casa"
dados_organizados <- merge(dados_organizados, dados,
                          by.x = "Time_de_fora_de_casa",
                          by.y = "Home...Away",
                          all.x = TRUE)
dados_organizados <- dados_organizados[, c(1:5, 26)]
colnames(dados_organizados)[6] <- "Rating_do_time_de_fora_de_casa"
dados_organizados <- dados_organizados[, c(2, 1, 3:6)]
dados_organizados$Resultado_do_time_da_casa <- "Empate"
dados_organizados$Resultado_do_time_da_casa[which(
  dados_organizados$Gols_feitos_pelo_time_da_casa > dados_organizados$Gols_feitos_pelo_time_de_fora_de_casa
)] <- "Vitória"
dados_organizados$Resultado_do_time_da_casa[which(
  dados_organizados$Gols_feitos_pelo_time_da_casa < dados_organizados$Gols_feitos_pelo_time_de_fora_de_casa
)] <- "Derrota"
levels(dados_organizados$Resultado_do_time_da_casa) <- c(
  "Derrota",
  "Empate",
  "Vitória"
)
dados_organizados$Diferenca_de_ratings <- dados_organizados$Rating_do_time_da_casa - dados_organizados$Rating_do_time_de_fora_de_casa

modelo <- multinom(
  Resultado_do_time_da_casa ~ Diferenca_de_ratings,
  data = dados_organizados
)
summary(modelo)
novo_dado <- data.frame(
  Diferenca_de_ratings = -5.3
)
predict(
  modelo,
  newdata = novo_dado,
  type = "probs"
)
dados_organizados$Diferenca_de_ratings
